package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory.RepositoryType;
import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionMapper;
import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionProjection;
import com.aa.ccrecon.accounting.aggregation.mapper.CcReconExceptionMapperImpl;
import com.aa.ccrecon.accounting.aggregation.service.AggregateTransactionService;
import com.aa.ccrecon.accounting.aggregation.service.MessageSenderService;
import com.aa.ccrecon.accounting.aggregation.service.RetryService;
import com.aa.ccrecon.accounting.aggregation.utils.ModelExtractor;
import com.aa.ccrecon.domain.aggregation.AccountingType;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.aggregation.MessageType;
import com.aa.ccrecon.domain.composite.CompositeModel;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.domain.constants.SalesSource;
import com.aa.ccrecon.domain.settlement.NotificationType;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.aa.ccrecon.domain.subledger.TransactionType.TYPES.SALE;
import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {AggregateTransactionQueryAndSenderCommand.class, RetryService.class,
		AggregateTransactionQueryAndSenderCommand.class, CcReconExceptionMapperImpl.class})
@EnableRetry
@ExtendWith({OutputCaptureExtension.class})
public class AggregateTransactionQueryandSenderCommandTests {

	@MockitoBean
	private AggregateTransactionService mockAggregatedTransactionService;

	@MockitoBean
	private MessageSenderService mockMessageSenderService;

	@MockitoBean
	private AggregateTransactionMapper mockMapper;

	@Autowired
	private AggregateTransactionQueryAndSenderCommand command;
	private static ObjectMapper objectMapper;

	@BeforeAll
	public static void init(){
		objectMapper = new ObjectMapper();
		objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
	}

	@Test
	@DisplayName(value = "given execute method is called, when database query throws exception, don't send any message and don't update database")
	public void givenExecuteIsCalled_whenDatabaseThrowsException_DontDoAnything() {
		when(mockAggregatedTransactionService.findUnprocessedAggregatedTransactions(any())).thenThrow(new RuntimeException());
		try {
			AggregateRequest aggregateRequest = new AggregateRequest();
			aggregateRequest.addValue(AggregateRequest.RequestValues.REPOSITORY_TYPE, RepositoryType.AGGREGATE);
			command.execute(aggregateRequest);
		}
		catch (Exception e) {
			Mockito.verify(mockAggregatedTransactionService, times(1)).findUnprocessedAggregatedTransactions(any());
		}
	}

	@Test
	@DisplayName(value = "given execute method is called, when database query returns rows and messageSender throws exception, don't update database")
	public void givenExecuteIsCalled_whenDatabaseHasDataAndMessageSenderThrowsException_DontDoAnything() {
		List<AggregateTransactionProjection> projectionList = getAggregateTransactionProjectionTestList(2, 10);
		AggregatedTransaction dto = new AggregatedTransaction();

		when(mockAggregatedTransactionService.findUnprocessedAggregatedTransactions(RepositoryType.AGGREGATE)).thenReturn(projectionList);
		when(mockMapper.mapAggregatedTransaction(any(), any(), any(), any())).thenReturn(dto);
		doThrow(new RuntimeException()).when(mockMessageSenderService).sendMessageToGlQueue(any());
		when(mockMapper .mapAggregatedTransaction(any(CcReconHeader.class))).thenReturn(dto);

		try {
			AggregateRequest aggregateRequest = new AggregateRequest();
			aggregateRequest.addValue(AggregateRequest.RequestValues.REPOSITORY_TYPE, RepositoryType.AGGREGATE);
			aggregateRequest.addValue(AggregateRequest.RequestValues.CC_RECON_HEADER, new CcReconHeader());
			command.execute(aggregateRequest);
			fail("Should have thrown exception");
		}
		catch (Exception e) {
			Mockito.verify(mockAggregatedTransactionService, times(1)).findUnprocessedAggregatedTransactions(RepositoryType.AGGREGATE);
			Mockito.verify(mockMapper, times(1)).mapAggregatedTransaction(any(), any());
			Mockito.verify(mockMessageSenderService, times(2)).sendMessageToGlQueue(any());
			Mockito.verify(mockAggregatedTransactionService, times(0))
					.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(any(RepositoryType.class), anyString());
			Mockito.verify(mockMessageSenderService, times(1)).sendMessageToExceptionQueue(any(AggregatedTransaction.class));
		}
	}

	@Test
	@DisplayName(value = "given execute method is called, when database query returns rows and message sent to queue, throw error from update database operation")
	public void givenExecuteIsCalled_whenDatabaseHasDataAndMessageSent_ThrowExceptionFromDBUpdate(CapturedOutput output) {
		List<AggregateTransactionProjection> projectionList = getAggregateTransactionProjectionTestList(2, 10);

		when(mockAggregatedTransactionService.findUnprocessedAggregatedTransactions(RepositoryType.AGGREGATE)).thenReturn(projectionList);
		when(mockMapper.mapAggregatedTransaction(any(), any())).thenReturn(new AggregatedTransaction());
		doNothing().when(mockMessageSenderService).sendMessageToGlQueue(any());
		when(mockAggregatedTransactionService.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(any(), anyString()))
				.thenThrow(new RuntimeException());

		try {
			AggregateRequest aggregateRequest = new AggregateRequest();
			aggregateRequest.addValue(AggregateRequest.RequestValues.REPOSITORY_TYPE, RepositoryType.AGGREGATE);
			command.execute(aggregateRequest);
		}
		catch (Exception e) {
			Mockito.verify(mockAggregatedTransactionService, times(1)).findUnprocessedAggregatedTransactions(RepositoryType.AGGREGATE);
			Mockito.verify(mockMapper, times(1)).mapAggregatedTransaction(any(), any());
			Mockito.verify(mockMessageSenderService, times(1)).sendMessageToGlQueue(any());
			Mockito.verify(mockAggregatedTransactionService, times(1))
					.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(any(RepositoryType.class), anyString());
			assertThat(output.getAll(), containsString("Aggregate exception: AGGREGATION_ACCOUNTING_FAILED"));
		}
	}

	@Test
	@DisplayName(value = "given execute method is called, when database query returns rows and message sent to queue, then update database")
	public void givenExecuteIsCalled_whenDatabaseHasData_SendMessageAndUpdateDatabase() throws IOException {
		try(InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(getClass().getResourceAsStream("/data/1186370/10 SampleContractARA-End.json")))) {
			CompositeModel jsonInputFile = objectMapper.readValue(isr, CompositeModel.class);
			CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(jsonInputFile).orElse(null);
			String sourceId = ModelExtractor.getSourceId.apply(jsonInputFile).orElse(null);
			int numberOfProjections = 2;
			int countForEach = 10;
			List<AggregateTransactionProjection> projectionList = getAggregateTransactionProjectionTestList(
					numberOfProjections, countForEach);

			when(mockAggregatedTransactionService.findUnprocessedAggregatedTransactions(RepositoryType.AGGREGATE)).thenReturn(projectionList);
			when(mockMapper.mapAggregatedTransaction(ccReconHeader, projectionList.get(0))).thenReturn(new AggregatedTransaction());
			when(mockMapper.mapAggregatedTransaction(ccReconHeader, projectionList.get(1))).thenReturn(new AggregatedTransaction());
			doNothing().when(mockMessageSenderService).sendMessageToGlQueue(any());
			when(mockAggregatedTransactionService
					.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(RepositoryType.AGGREGATE, projectionList.get(0).getGlobalId()))
					.thenReturn(projectionList.get(0).getCount().intValue());
			when(mockAggregatedTransactionService
					.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(RepositoryType.AGGREGATE, projectionList.get(1).getGlobalId()))
					.thenReturn(projectionList.get(1).getCount().intValue());

			AggregateRequest aggregateRequest = new AggregateRequest();
			aggregateRequest.addValue(AggregateRequest.RequestValues.CC_RECON_HEADER, ccReconHeader);
			aggregateRequest.addValue(AggregateRequest.RequestValues.SOURCE_ID, sourceId);
			aggregateRequest.addValue(AggregateRequest.RequestValues.REPOSITORY_TYPE, RepositoryType.AGGREGATE);
			command.execute(aggregateRequest);

			Mockito.verify(mockAggregatedTransactionService, times(1)).findUnprocessedAggregatedTransactions(RepositoryType.AGGREGATE);
			Mockito.verify(mockMapper, times(2)).mapAggregatedTransaction(any(), any());
			Mockito.verify(mockMessageSenderService, times(2)).sendMessageToGlQueue(any());
			Mockito.verify(mockAggregatedTransactionService, times(1))
					.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(RepositoryType.AGGREGATE, projectionList.get(0).getGlobalId());
			Mockito.verify(mockAggregatedTransactionService, times(1))
					.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(RepositoryType.AGGREGATE, projectionList.get(1).getGlobalId());
		}

	}

	private List<AggregateTransactionProjection> getAggregateTransactionProjectionTestList(int numberOfProjectons,
			int countForEach) {

		List<AggregateTransactionProjection> projectionList = new ArrayList<>();

		for (int i = 1; i <= numberOfProjectons; i++) {
			AggregateTransactionProjection projection = new AggregateTransactionProjectionTestImpl(String.valueOf(i),
					(long) countForEach, MessageType.AGGREGATED_GL_NON_UATP, SalesSource.COMPANY, SALE);
			projectionList.add(projection);
		}

		return projectionList;

	}

}
