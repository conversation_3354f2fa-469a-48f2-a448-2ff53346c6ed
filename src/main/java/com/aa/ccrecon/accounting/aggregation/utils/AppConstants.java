package com.aa.ccrecon.accounting.aggregation.utils;

import com.aa.ccrecon.domain.composite.header.MessageContext;

public class AppConstants {

    private AppConstants() {
    }

    public static final String HEADER_VERSION = "1.0.0";
    public static final String SOURCE_ID = "itfacs-pmt-ccrecon-aggregation-accounting";
    public static final String APPLICATION_ID = "ccrecon";
    public static final String AGGREGATION_ACCOUNTING_FAILED = "AGGREGATION_ACCOUNTING_FAILED";
    public static final String EXCEPTION_STATUS = "OPEN";
    public static final String MESSAGE_CONTENT_TYPE = "application/json";
    public static final String MESSAGE_CONTENT_ENCODING = "UTF-8";
    public static final String MESSAGE_TYPE_CODE = "AGGREGATION";


    public static String getMessageContentType() {
        return MESSAGE_CONTENT_TYPE;
    }
    public static String getMessageContentEncoding() {
        return MESSAGE_CONTENT_ENCODING;
    }
    public static String getMessageTypeCode() {
        return MESSAGE_TYPE_CODE;


    }
}
