package com.aa.ccrecon.accounting.aggregation.processors;

import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory.RepositoryType;
import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionMapper;
import com.aa.ccrecon.accounting.aggregation.mapper.AggregateTransactionProjection;
import com.aa.ccrecon.accounting.aggregation.mapper.CcReconExceptionMapper;
import com.aa.ccrecon.accounting.aggregation.service.AggregateTransactionService;
import com.aa.ccrecon.accounting.aggregation.service.MessageSenderService;
import com.aa.ccrecon.accounting.aggregation.service.RetryService;
import com.aa.ccrecon.accounting.aggregation.utils.ModelExtractor;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.composite.header.CcReconException;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.itfacs.pmt.mask.Masked;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

import static com.aa.ccrecon.accounting.aggregation.utils.AppConstants.AGGREGATION_ACCOUNTING_FAILED;

@Component
@RequiredArgsConstructor
public class AggregateTransactionQueryAndSenderCommand extends AbstractExecutor {

	private static final MaskingLogger log = MaskingLoggerFactory.getLogger(AggregateTransactionQueryAndSenderCommand.class);
	private final AggregateTransactionService aggregateTransactionService;
	private final MessageSenderService messageSenderService;
	private final AggregateTransactionMapper aggregateTransactionMapper;
	private final CcReconExceptionMapper ccReconExceptionMapper;
	private final RetryService retryService;


	/***
	 *
	 * @param request
	 */
	@Override
	public void execute(AggregateRequest request) {
		CcReconHeader ccReconHeader = request.getValue(AggregateRequest.RequestValues.CC_RECON_HEADER, CcReconHeader.class);
		String traceId = request.getValue(AggregateRequest.RequestValues.TRACE_ID, String.class);

		try {

			RepositoryType repositoryType = request.getValue(AggregateRequest.RequestValues.REPOSITORY_TYPE, RepositoryType.class);
			List<AggregateTransactionProjection> entityList = aggregateTransactionService.findUnprocessedAggregatedTransactions(repositoryType);
			sendMessageToQueueAndUpdateDatabase(entityList, ccReconHeader, repositoryType, traceId);

		} catch (Exception e) {
			log.error("Aggregate exception: " + AGGREGATION_ACCOUNTING_FAILED, e);

			String sourceId = request.getValue(AggregateRequest.RequestValues.SOURCE_ID, String.class);
			CcReconException ccReconException = ccReconExceptionMapper.mapCcReconException(AGGREGATION_ACCOUNTING_FAILED, sourceId);
			ModelExtractor.setCcReconException.accept(ccReconHeader, ccReconException);
			AggregatedTransaction aggregatedTransaction = aggregateTransactionMapper.mapAggregatedTransaction(ccReconHeader);
			retryService.retry(() -> messageSenderService.sendMessageToExceptionQueue(aggregatedTransaction));

			throw e;
		}
		super.execute(request);
	}


	/***
	 *
	 * @param entityList
	 * @param ccReconHeader
	 * @param repositoryType
	 * @param traceId
	 */
	private void sendMessageToQueueAndUpdateDatabase(List<AggregateTransactionProjection> entityList, CcReconHeader ccReconHeader, RepositoryType repositoryType, String traceId) {
		entityList.forEach(aggregateTransactionProjection -> {
			AggregatedTransaction aggregatedTransaction = aggregateTransactionMapper.mapAggregatedTransaction(ccReconHeader, aggregateTransactionProjection, UUID.randomUUID().toString(), traceId);
			try {
				retryService.retry(() -> messageSenderService.sendMessageToGlQueue(aggregatedTransaction));
				updateDatabase(aggregateTransactionProjection, repositoryType);
			} catch (Exception e) {
				log.error("Exception in sending AggregatedTransactionDto to serviceBus, message: " + Masked.objectToMaskedString(aggregatedTransaction), e);
				throw e;
			}
		});
	}


	/***
	 *
	 * @param aggregateTransactionProjection
	 * @param repositoryType
	 */
	private void updateDatabase(AggregateTransactionProjection aggregateTransactionProjection, RepositoryType repositoryType) {
		try {

			Integer rowsUpdated = aggregateTransactionService.updateAggregatedTransactionsWithGlobalIdAndIsProcessedFalse(repositoryType, aggregateTransactionProjection.getGlobalId());
			log.debug("GlobalId: {}, rowsUpdated: {}", aggregateTransactionProjection.getGlobalId(), rowsUpdated);

		} catch (Exception e) {
			log.error("Exception updating globalId: " + aggregateTransactionProjection.getGlobalId() + " to isProcessed = true.", e);
			throw e;
		}
	}
}
