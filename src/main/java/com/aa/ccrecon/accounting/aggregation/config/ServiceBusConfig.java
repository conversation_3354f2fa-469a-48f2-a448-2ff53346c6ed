package com.aa.ccrecon.accounting.aggregation.config;

import com.aa.ccrecon.accounting.aggregation.factory.MaskingLoggerFactory;
import com.aa.ccrecon.accounting.aggregation.logger.MaskingLogger;
import com.aa.ccrecon.accounting.aggregation.service.AggregateService;
import com.aa.ccrecon.accounting.aggregation.utils.ModelExtractor;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.composite.CompositeModel;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.domain.composite.header.MessageContext;
import com.azure.spring.messaging.checkpoint.Checkpointer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import com.aa.ccrecon.domain.aggregation.AggregateAccountingDetails;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.aa.ccrecon.domain.aggregation.AccountingType.RECEIVABLE_ACCOUNTING;
import static com.aa.ccrecon.domain.composite.header.MessageContext.MessageTypeCode.AGGREGATION;
import static com.azure.spring.messaging.AzureHeaders.CHECKPOINTER;

@Configuration
public class ServiceBusConfig {

	private static final MaskingLogger log = MaskingLoggerFactory.getLogger(ServiceBusConfig.class);

	@Bean
	public Sinks.Many<Message<AggregatedTransaction>> aggregatedTransactionSink() {
		return Sinks.many().unicast().onBackpressureBuffer();
	}

	@Bean
	public Sinks.Many<Message<AggregatedTransaction>> exceptionSink() {
		return Sinks.many().unicast().onBackpressureBuffer();
	}

	@Bean
	public Supplier<Flux<Message<AggregatedTransaction>>> supplyServiceBusAggregatedTransactionMessage(
			@Qualifier(value = "aggregatedTransactionSink") Sinks.Many<Message<AggregatedTransaction>> aggregatedTransactionSink) {

		return () -> aggregatedTransactionSink.asFlux()
				.doOnNext(m -> log.info("Manually sending message: {}", m.getPayload()))
				.doOnError(t -> log.error("Error encountered", t));
	}

	@Bean
	public Supplier<Flux<Message<AggregatedTransaction>>> supplyServiceBusException(
			@Qualifier(value = "exceptionSink") Sinks.Many<Message<AggregatedTransaction>> exceptionSink) {
		return () -> exceptionSink.asFlux()
				.doOnNext(m -> log.info("Manually sending message {}", m.getPayload()))
				.doOnError(t -> log.error("Error encountered", t));
	}

	@Bean
	public Consumer<Message<CompositeModel>> consumeServiceBusDetailPaymentMessage(AggregateService aggregateService) {
		return message -> {
			try {
				log.info("New message received: '{}'", message.getPayload());

				CompositeModel payload = message.getPayload();

				// Check if this is the new enhanced aggregate accounting message format
				if (processEnhancedAggregateAccountingMessage(payload, aggregateService)) {
					return;
				}

				// Handle existing EOF message format
				Optional<MessageContext.MESSAGE_SUB_TYPE_CODE> eventState = ModelExtractor.getMessageSubTypeCode.apply(payload);
				Optional<String> sourceId = ModelExtractor.getSourceId.apply(payload);
				CcReconHeader ccReconHeader = ModelExtractor.getCcReconHeader.apply(payload).orElse(new CcReconHeader());

				if (eventState.isPresent()) {
					String sourceIdText = sourceId.orElseGet(() -> "Unknown. SourceId missing");
					if (eventState.get().equals(MessageContext.MESSAGE_SUB_TYPE_CODE.EOF)) {
						log.debug("End of File message received. File: {}", sourceIdText);
						aggregateService.process(ccReconHeader, sourceIdText);
					} else {
						log.info("Invalid Event State. File: {}, Event State: {}", sourceIdText,
								eventState.get());
					}
				}
			} catch (Exception e) {
				log.error("Error processing message", e);
			} finally {
				Checkpointer checkpointer = (Checkpointer) message.getHeaders().get(CHECKPOINTER);
				if (checkpointer != null) {
					checkpointer.success()
							.doOnSuccess(s -> log.debug("Message '{}' successfully checkpointed", message.getPayload()))
							.doOnError(e -> log.error("Error found", e)).block();
				}
			}
		};
	}

	private boolean processEnhancedAggregateAccountingMessage(CompositeModel payload, AggregateService aggregateService) {
		Optional<String> messageTypeCode = ModelExtractor.getMessageTypeCode.apply(payload);
		Optional<AggregateAccountingDetails> aggregateAccountingDetails =
				ModelExtractor.getAggregateAccountingDetails.apply(payload);
		Optional<String> traceId = ModelExtractor.getTraceId.apply(payload);


		if (messageTypeCode.isPresent() && AGGREGATION.equals(messageTypeCode.get()) && aggregateAccountingDetails.isPresent()) {
			AggregateAccountingDetails details = aggregateAccountingDetails.get();

			if (RECEIVABLE_ACCOUNTING.equals(details.getAccountingType())) {
				log.info("Triggering receivable accounting aggregation for file: {}", details.getFileName());

				log.info("Triggering non-UATP & UATP data aggregation for receivable accounting - File: {}, Processor: {}",

						details.getFileName(), details.getProcessor());

				CcReconHeader ccReconHeader = createCcReconHeaderFromDetails(details);
				String traceIdValue = traceId.orElse(details.getFileName());
				aggregateService.process(ccReconHeader, details.getFileName(), traceIdValue);

            } else { //until we get settlement implementation
				log.info("AccountingType {} is not RECEIVABLE_ACCOUNTING, skipping aggregation processing", details.getAccountingType());
            }
            return true;
        }

		return false;
	}


	private CcReconHeader createCcReconHeaderFromDetails(AggregateAccountingDetails details) {
		CcReconHeader ccReconHeader = new CcReconHeader();

		if (details.getFileProcessedId() != null) {
			ccReconHeader.setFileDetailsId(details.getFileProcessedId());
		}

		return ccReconHeader;
	}

}