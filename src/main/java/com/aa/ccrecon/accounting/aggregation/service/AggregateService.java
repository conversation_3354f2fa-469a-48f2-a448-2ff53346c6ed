package com.aa.ccrecon.accounting.aggregation.service;

import com.aa.ccrecon.accounting.aggregation.factory.AggregatedTransactionRepositoryFactory.RepositoryType;
import com.aa.ccrecon.accounting.aggregation.processors.*;
import com.aa.ccrecon.accounting.aggregation.processors.uatp.UATPAggregateDataCommand;
import com.aa.ccrecon.accounting.aggregation.processors.uatp.UATPAssignGlobalIdCommand;
import com.aa.ccrecon.accounting.aggregation.processors.uatp.UATPCreationCommand;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.UnexpectedRollbackException;

@Service
@Slf4j
@RequiredArgsConstructor
public class AggregateService {

	private final VerifyAvailableAggregateExecutor verifyAvailableAggregateExecutor;

	private final ImportAggregateCommand importAggregateCommand;

	private final AssignGlobalIdAggregateCommand assignGlobalIdAggregateCommand;

	private final AggregateTransactionQueryAndSenderCommand aggregateTransactionQueryAndSenderCommand;

	private final UATPAggregateDataCommand uatpAggregateDataCommand;

	private final UATPAssignGlobalIdCommand uatpAssignGlobalIdCommand;

	private final UATPCreationCommand uatpCreationCommand;

	private final AggregateExecutor aggregateGlExecutor;

	private final AggregateExecutor aggregateGlUatpExecutor;


	/***
	 *
	 * @param ccReconHeader
	 * @param sourceId
	 */
	public void process(CcReconHeader ccReconHeader, String sourceId)
	{
		try {
			processAggregateGl(ccReconHeader, sourceId);
			processAggregateGlUatp(ccReconHeader, sourceId);
		}
		catch (UnexpectedRollbackException ure) {
			log.error("Transaction rollback", ure);
		}
	}

	/***
	 *
	 * @param ccReconHeader
	 * @param sourceId
	 * @param traceId
	 */
	public void process(CcReconHeader ccReconHeader, String sourceId, String traceId)
	{
		try {
			processAggregateGl(ccReconHeader, sourceId, traceId);
			processAggregateGlUatp(ccReconHeader, sourceId, traceId);
		}
		catch (UnexpectedRollbackException ure) {
			log.error("Transaction rollback", ure);
		}
	}


	/***
	 *
	 * @param ccReconHeader
	 * @param sourceId
	 */
	private void processAggregateGl(CcReconHeader ccReconHeader, String sourceId)
	{
		assignGlobalIdAggregateCommand.setNextProcessor(aggregateTransactionQueryAndSenderCommand);
		importAggregateCommand.setNextProcessor(assignGlobalIdAggregateCommand);
		verifyAvailableAggregateExecutor.setNextProcessor(importAggregateCommand);
		aggregateGlExecutor.setNextProcessor(verifyAvailableAggregateExecutor);
		aggregateGlExecutor.execute(ccReconHeader, sourceId, RepositoryType.AGGREGATE);
	}

	/***
	 *
	 * @param ccReconHeader
	 * @param sourceId
	 * @param traceId
	 */
	private void processAggregateGl(CcReconHeader ccReconHeader, String sourceId, String traceId)
	{
		assignGlobalIdAggregateCommand.setNextProcessor(aggregateTransactionQueryAndSenderCommand);
		importAggregateCommand.setNextProcessor(assignGlobalIdAggregateCommand);
		verifyAvailableAggregateExecutor.setNextProcessor(importAggregateCommand);
		aggregateGlExecutor.setNextProcessor(verifyAvailableAggregateExecutor);
		aggregateGlExecutor.execute(ccReconHeader, sourceId, RepositoryType.AGGREGATE, traceId);
	}


	/***
	 *
	 * @param ccReconHeader
	 * @param sourceId
	 */
	private void processAggregateGlUatp(CcReconHeader ccReconHeader, String sourceId)
	{
		uatpCreationCommand.setNextProcessor(aggregateTransactionQueryAndSenderCommand);
		uatpAssignGlobalIdCommand.setNextProcessor(uatpCreationCommand);
		uatpAggregateDataCommand.setNextProcessor(uatpAssignGlobalIdCommand);
		aggregateGlUatpExecutor.setNextProcessor(uatpAggregateDataCommand);
		aggregateGlUatpExecutor.execute(ccReconHeader, sourceId, RepositoryType.UATP_AGGREGATE);
	}

	/***
	 *
	 * @param ccReconHeader
	 * @param sourceId
	 * @param traceId
	 */
	private void processAggregateGlUatp(CcReconHeader ccReconHeader, String sourceId, String traceId)
	{
		uatpCreationCommand.setNextProcessor(aggregateTransactionQueryAndSenderCommand);
		uatpAssignGlobalIdCommand.setNextProcessor(uatpCreationCommand);
		uatpAggregateDataCommand.setNextProcessor(uatpAssignGlobalIdCommand);
		aggregateGlUatpExecutor.setNextProcessor(uatpAggregateDataCommand);
		aggregateGlUatpExecutor.execute(ccReconHeader, sourceId, RepositoryType.UATP_AGGREGATE, traceId);
	}

}
