package com.aa.ccrecon.accounting.aggregation.mapper;

import com.aa.ccrecon.accounting.aggregation.utils.AppConstants;
import com.aa.ccrecon.domain.aggregation.AggregatedGeneralLedgerTransactionDetails;
import com.aa.ccrecon.domain.aggregation.AggregatedTransaction;
import com.aa.ccrecon.domain.composite.header.CcReconHeader;
import com.aa.ccrecon.domain.composite.header.MessageContext;
import com.aa.ccrecon.domain.composite.header.SourceContext;
import com.aa.ccrecon.domain.composite.header.TraceContext;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.util.UUID;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, imports = { LocalDateTime.class })
public interface AggregateTransactionMapper {

    @Mapping(target = "messageEnvelope.messageHeader.headerVersion", expression = "java(getHeaderVersion())")
    @Mapping(target = "messageEnvelope.messageHeader.timestamp", expression = "java(getTimestamp())")
    @Mapping(target = "messageEnvelope.messageHeader.sourceContext", source = "transactionId", qualifiedByName = "mapSourceContext")
    @Mapping(target = "messageEnvelope.messageHeader.messageContext", expression = "java(mapMessageContext())")
    @Mapping(target = "messageEnvelope.messageHeader.traceContext", expression = "java(mapTraceContext(traceId))")
    @Mapping(target = "messageEnvelope.payload.ccReconHeader", source = "ccReconHeader")
    @Mapping(target = "messageEnvelope.payload.aggregatedGeneralLedgerTransactionDetails", source = "projection", qualifiedByName = "mapAggregatedGeneralLedgerTransactionDetails")
    AggregatedTransaction mapAggregatedTransaction(CcReconHeader ccReconHeader, AggregateTransactionProjection projection, String transactionId, String traceId);

    default AggregatedTransaction mapAggregatedTransaction(CcReconHeader ccReconHeader, AggregateTransactionProjection projection) {
        return mapAggregatedTransaction(ccReconHeader, projection, UUID.randomUUID().toString(), AppConstants.SOURCE_ID);
    }

    default AggregatedTransaction mapAggregatedTransaction(CcReconHeader ccReconHeader) {
        return mapAggregatedTransaction(ccReconHeader, null, UUID.randomUUID().toString(), AppConstants.SOURCE_ID);
    }

    default MessageContext mapMessageContext() {
        MessageContext messageContext = new MessageContext();
        messageContext.setMessageContentType(AppConstants.getMessageContentType());
        messageContext.setMessageContentEncoding(AppConstants.getMessageContentEncoding());
        messageContext.setMessageTypeCode(AppConstants.getMessageTypeCode());
        messageContext.setMessageSubTypeCode(MessageContext.MESSAGE_SUB_TYPE_CODE.RECEIVABLE);
        messageContext.setPayloadHashValue("");
        messageContext.setHashingAlgorithm("");
        return messageContext;
    }

    default TraceContext mapTraceContext(String traceId) {
        TraceContext traceContext = new TraceContext();
        traceContext.setTraceId(traceId != null ? traceId : AppConstants.SOURCE_ID);
        return traceContext;
    }

    default String getHeaderVersion() {
        return AppConstants.HEADER_VERSION;
    }

    default String getTimestamp() {
        return LocalDateTime.now().toString();
    }

    @Named("mapSourceContext")
    @Mapping(target = "sourceId", expression = "java(getSourceId())")
    @Mapping(target = "applicationId", expression = "java(getApplicationId())")
    @Mapping(target = "transactionId", source = "transactionId")
    SourceContext mapSourceContext(String transactionId);

    default String getSourceId() {
        return AppConstants.SOURCE_ID;
    }

    default String getApplicationId() {
        return AppConstants.APPLICATION_ID;
    }

    @Named("mapAggregatedGeneralLedgerTransactionDetails")
    @Mapping(target = "globalId", source = "projection.globalId")
    @Mapping(target = "count", source = "projection.count")
    @Mapping(target = "messageType", source = "projection.messageType")
    @Mapping(target = "dateTimestamp", expression = "java(getDateTimestamp())")
    @Mapping(target = "salesSource", source = "projection.salesSource")
    @Mapping(target = "transactionType", source = "projection.transactionType")
    @Mapping(target = "accountingType", constant = "RECEIVABLE_ACCOUNTING")
    @Mapping(target = "processor", constant = "ARA")
    @Mapping(target = "notificationOrigin", constant = "SYSTEM")
    AggregatedGeneralLedgerTransactionDetails mapAggregatedGeneralLedgerTransactionDetails(AggregateTransactionProjection projection);

    default String getDateTimestamp() {
        return java.time.ZonedDateTime.now(java.time.ZoneOffset.UTC).toString();
    }
}