package com.aa.ccrecon.accounting.aggregation.utils;

import com.aa.ccrecon.domain.composite.CompositeModel;
import com.aa.ccrecon.domain.composite.MessageEnvelope;
import com.aa.ccrecon.domain.composite.Payload;
import com.aa.ccrecon.domain.composite.header.*;
import com.aa.ccrecon.domain.aggregation.AggregateAccountingDetails;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;

public class ModelExtractor {

    private ModelExtractor() {
    }

    public static final Function<CompositeModel, Optional<com.aa.ccrecon.domain.composite.MessageEnvelope>> getMessageEnvelope = compositeModel -> Optional.ofNullable(compositeModel)
            .map(CompositeModel::getMessageEnvelope);

    public static final Function<CompositeModel, Optional<MessageHeader>> getMessageHeader = compositeModel -> getMessageEnvelope.apply(compositeModel)
            .map(MessageEnvelope::getMessageHeader);

    public static final Function<CompositeModel, Optional<MessageContext.MESSAGE_SUB_TYPE_CODE>> getMessageSubTypeCode = compositeModel -> getMessageHeader.apply(compositeModel)
            .map(MessageHeader::getMessageContext)
            .map(MessageContext::getMessageSubTypeCode);

    public static final Function<CompositeModel, Optional<String>> getSourceId = compositeModel -> getMessageHeader.apply(compositeModel)
            .map(MessageHeader::getSourceContext)
            .map(SourceContext::getSourceId);

    public static final Function<CompositeModel, Optional<CcReconHeader>> getCcReconHeader = compositeModel -> getMessageEnvelope.apply(compositeModel)
            .map(MessageEnvelope::getPayload)
            .map(Payload::getCcReconHeader);

    public static final Function<CompositeModel, Optional<AggregateAccountingDetails>> getAggregateAccountingDetails = compositeModel -> getMessageEnvelope.apply(compositeModel)
            .map(MessageEnvelope::getPayload)
            .map(Payload::getAggregateAccountingDetails);

    public static final Function<CompositeModel, Optional<String>> getMessageTypeCode = compositeModel -> getMessageHeader.apply(compositeModel)
            .map(MessageHeader::getMessageContext)
            .map(MessageContext::getMessageTypeCode);

    public static final Function<CompositeModel, Optional<String>> getTraceId = compositeModel -> getMessageHeader.apply(compositeModel)
            .map(MessageHeader::getTraceContext)
            .map(TraceContext::getTraceId);

    public static final BiConsumer<CcReconHeader, CcReconException> setCcReconException = (ccReconHeader, ccReconException) -> {
        if (ccReconHeader != null && ccReconException != null) {
            Optional.of(ccReconHeader)
                    .map(CcReconHeader::getCcReconExceptions)
                    .ifPresentOrElse(ccReconExceptions -> ccReconExceptions.add(ccReconException), () -> {
                        List<CcReconException> ccReconExceptions = new ArrayList<>(List.of(ccReconException));
                        ccReconHeader.setCcReconExceptions(ccReconExceptions);
                    });
        }
    };
}
