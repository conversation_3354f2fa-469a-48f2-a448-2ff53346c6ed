package com.aa.ccrecon.accounting.aggregation.processors;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

public class AggregateRequest {

    public enum RequestValues {
        AGGREGATE_COUNT,
        CC_RECON_HEADER,
        SOURCE_ID,
        REPOSITORY_TYPE,
        TRACE_ID
    }

    private final Map<RequestValues, Object> values = new EnumMap<>(RequestValues.class);

    public void addValue(RequestValues key, Object value) {
        values.put(key, value);
    }

    public <T> T getValue(RequestValues key, Class<T> clazz) {
        return Optional.of(values)
                .map(m -> m.get(key))
                .map(clazz::cast)
                .orElse(null);
    }
}
